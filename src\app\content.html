<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="content-security-policy" content="script-src 'self' 'unsafe-inline'" />
        <title>Pickle Glass Content</title>
        <style>
            :root {
                --background-transparent: transparent;
                --text-color: #e5e5e7;
                --border-color: rgba(255, 255, 255, 0.2);
                --header-background: rgba(0, 0, 0, 0.8);
                --header-actions-color: rgba(255, 255, 255, 0.6);
                --main-content-background: rgba(0, 0, 0, 0.8);
                --button-background: rgba(0, 0, 0, 0.5);
                --button-border: rgba(255, 255, 255, 0.1);
                --icon-button-color: rgb(229, 229, 231);
                --hover-background: rgba(255, 255, 255, 0.1);
                --input-background: rgba(0, 0, 0, 0.3);
                --placeholder-color: rgba(255, 255, 255, 0.4);
                --focus-border-color: #007aff;
                --focus-box-shadow: rgba(0, 122, 255, 0.2);
                --input-focus-background: rgba(0, 0, 0, 0.5);
                --scrollbar-track: rgba(0, 0, 0, 0.2);
                --scrollbar-thumb: rgba(255, 255, 255, 0.2);
                --scrollbar-thumb-hover: rgba(255, 255, 255, 0.3);
                --preview-video-background: rgba(0, 0, 0, 0.9);
                --preview-video-border: rgba(255, 255, 255, 0.15);
                --option-label-color: rgba(255, 255, 255, 0.8);
                --screen-option-background: rgba(0, 0, 0, 0.4);
                --screen-option-hover-background: rgba(0, 0, 0, 0.6);
                --screen-option-selected-background: rgba(0, 122, 255, 0.15);
                --screen-option-text: rgba(255, 255, 255, 0.7);
                --description-color: rgba(255, 255, 255, 0.6);
                --start-button-background: white;
                --start-button-color: black;
                --start-button-border: white;
                --start-button-hover-background: rgba(255, 255, 255, 0.8);
                --start-button-hover-border: rgba(0, 0, 0, 0.2);
                --text-input-button-background: #007aff;
                --text-input-button-hover: #0056b3;
                --link-color: #007aff;
                --key-background: rgba(255, 255, 255, 0.1);
                --scrollbar-background: rgba(0, 0, 0, 0.4);

                /* Layout-specific variables */
                --header-padding: 10px 20px;
                --header-font-size: 16px;
                --header-gap: 12px;
                --header-button-padding: 8px 16px;
                --header-icon-padding: 8px;
                --header-font-size-small: 13px;
                --main-content-padding: 20px;
                --main-content-margin-top: 10px;
                --icon-size: 24px;
                --border-radius: 7px;
                --content-border-radius: 7px;
            }

            /* Compact layout styles */
            :root.compact-layout {
                --header-padding: 6px 12px;
                --header-font-size: 13px;
                --header-gap: 6px;
                --header-button-padding: 4px 8px;
                --header-icon-padding: 4px;
                --header-font-size-small: 10px;
                --main-content-padding: 10px;
                --main-content-margin-top: 2px;
                --icon-size: 16px;
                --border-radius: 4px;
                --content-border-radius: 4px;
            }

            html,
            body {
                margin: 0;
                padding: 0;
                min-height: 100%;
                overflow: hidden;
                background: transparent;
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            }

            * {
                box-sizing: border-box;
            }

            pickle-glass-app {
                display: block;
                width: 100%;
                transform: translate3d(0, 0, 0);
                backface-visibility: hidden;
                perspective: 1000px;
                transform-origin: center center;
                contain: layout style paint;
                transition: transform 0.25s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.25s ease-out;
            }

            .window-sliding-down {
                animation: slideDownFromHeader 0.25s cubic-bezier(0.23, 1, 0.32, 1) forwards;
                will-change: transform, opacity;
                transform-style: preserve-3d;
            }

            .window-sliding-up {
                animation: slideUpToHeader 0.18s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
                will-change: transform, opacity;
                transform-style: preserve-3d;
            }

            .window-hidden {
                opacity: 0;
                transform: translate3d(0, -18px, 0) scale3d(0.96, 0.96, 1);
                pointer-events: none;
                will-change: auto;
                contain: layout style paint;
            }

            .listen-window-moving {
                transition: transform 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                will-change: transform;
            }

            .listen-window-center {
                transform: translate3d(0, 0, 0);
            }

            .listen-window-left {
                transform: translate3d(-110px, 0, 0);
            }

            @keyframes slideDownFromHeader {
                0% {
                    opacity: 0;
                    transform: translate3d(0, -18px, 0) scale3d(0.96, 0.96, 1);
                }
                25% {
                    opacity: 0.4;
                    transform: translate3d(0, -10px, 0) scale3d(0.98, 0.98, 1);
                }
                50% {
                    opacity: 0.7;
                    transform: translate3d(0, -3px, 0) scale3d(1.01, 1.01, 1);
                }
                75% {
                    opacity: 0.9;
                    transform: translate3d(0, -0.5px, 0) scale3d(1.005, 1.005, 1);
                }
                100% {
                    opacity: 1;
                    transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
                }
            }

            .settings-window-show {
                animation: settingsPopFromButton 0.22s cubic-bezier(0.23, 1, 0.32, 1) forwards;
                transform-origin: 85% 0%;
                will-change: transform, opacity;
                transform-style: preserve-3d;
            }

            .settings-window-hide {
                animation: settingsCollapseToButton 0.18s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
                transform-origin: 85% 0%;
                will-change: transform, opacity;
                transform-style: preserve-3d;
            }

            @keyframes settingsPopFromButton {
                0% {
                    opacity: 0;
                    transform: translate3d(0, -8px, 0) scale3d(0.5, 0.5, 1);
                }
                40% {
                    opacity: 0.8;
                    transform: translate3d(0, -2px, 0) scale3d(1.05, 1.05, 1);
                }
                70% {
                    opacity: 0.95;
                    transform: translate3d(0, 0, 0) scale3d(1.02, 1.02, 1);
                }
                100% {
                    opacity: 1;
                    transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
                }
            }

            @keyframes settingsCollapseToButton {
                0% {
                    opacity: 1;
                    transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
                }
                30% {
                    opacity: 0.8;
                    transform: translate3d(0, -1px, 0) scale3d(0.9, 0.9, 1);
                }
                70% {
                    opacity: 0.3;
                    transform: translate3d(0, -5px, 0) scale3d(0.7, 0.7, 1);
                }
                100% {
                    opacity: 0;
                    transform: translate3d(0, -8px, 0) scale3d(0.5, 0.5, 1);
                }
            }

            @keyframes slideUpToHeader {
                0% {
                    opacity: 1;
                    transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
                }
                30% {
                    opacity: 0.6;
                    transform: translate3d(0, -6px, 0) scale3d(0.98, 0.98, 1);
                }
                65% {
                    opacity: 0.2;
                    transform: translate3d(0, -14px, 0) scale3d(0.95, 0.95, 1);
                }
                100% {
                    opacity: 0;
                    transform: translate3d(0, -18px, 0) scale3d(0.93, 0.93, 1);
                }
            }
        </style>
    </head>
    <body>
        <script src="../assets/marked-4.3.0.min.js"></script>
        
        <script type="module" src="../../public/build/content.js"></script>

        <pickle-glass-app id="pickle-glass"></pickle-glass-app>
        
        <script>
            window.addEventListener('DOMContentLoaded', () => {
                const app = document.getElementById('pickle-glass');
                let animationTimeout = null;

                if (window.require) {
                    const { ipcRenderer } = window.require('electron');
                    
                    ipcRenderer.on('window-show-animation', () => {
                        console.log('Starting window show animation');
                        app.classList.remove('window-hidden', 'window-sliding-up', 'settings-window-hide');
                        app.classList.add('window-sliding-down');
                        
                        if (animationTimeout) clearTimeout(animationTimeout);
                        animationTimeout = setTimeout(() => {
                            app.classList.remove('window-sliding-down');
                        }, 250);
                    });
                    
                    ipcRenderer.on('settings-window-show-animation', () => {
                        console.log('Starting settings window show animation');
                        app.classList.remove('window-hidden', 'window-sliding-up', 'settings-window-hide');
                        app.classList.add('settings-window-show');
                        
                        if (animationTimeout) clearTimeout(animationTimeout);
                        animationTimeout = setTimeout(() => {
                            app.classList.remove('settings-window-show');
                        }, 220);
                    });
                    
                    ipcRenderer.on('window-hide-animation', () => {
                        console.log('Starting window hide animation');
                        app.classList.remove('window-sliding-down', 'settings-window-show');
                        app.classList.add('window-sliding-up');
                        
                        if (animationTimeout) clearTimeout(animationTimeout);
                        animationTimeout = setTimeout(() => {
                            app.classList.remove('window-sliding-up');
                            app.classList.add('window-hidden');
                        }, 180);
                    });
                    
                    ipcRenderer.on('settings-window-hide-animation', () => {
                        console.log('Starting settings window hide animation');
                        app.classList.remove('window-sliding-down', 'settings-window-show');
                        app.classList.add('settings-window-hide');
                        
                        if (animationTimeout) clearTimeout(animationTimeout);
                        animationTimeout = setTimeout(() => {
                            app.classList.remove('settings-window-hide');
                            app.classList.add('window-hidden');
                        }, 180);
                    });

                    ipcRenderer.on('listen-window-move-to-center', () => {
                        console.log('Moving listen window to center');
                        app.classList.add('listen-window-moving');
                        app.classList.remove('listen-window-left');
                        app.classList.add('listen-window-center');
                        
                        setTimeout(() => {
                            app.classList.remove('listen-window-moving');
                        }, 350);
                    });

                    ipcRenderer.on('listen-window-move-to-left', () => {
                        console.log('Moving listen window to left');
                        app.classList.add('listen-window-moving');
                        app.classList.remove('listen-window-center');
                        app.classList.add('listen-window-left');
                        
                        setTimeout(() => {
                            app.classList.remove('listen-window-moving');
                        }, 350);
                    });
                }
            });
        </script>
    </body>
</html>
