version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: pickleglass-backend
    restart: always
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=/app/data/pickleglass.db
    volumes:
      - ./backend:/app
      - ./data:/app/data

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: pickleglass-frontend
    restart: always
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    depends_on:
      - backend
    volumes:
      - .:/app
      - /app/node_modules

volumes:
  mongodb_data: 